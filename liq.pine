//@version=6
indicator('Liquidation Levels on OI w/ profile - Enhanced', overlay = true, max_lines_count = 500, max_boxes_count = 120, max_labels_count = 1)

// ===== INPUT PARAMETERS =====
string userSymbol = syminfo.tickerid
maLength = input(60, title = 'MA Length')
numOfLines = 500

// Liquidation level thresholds
h3 = input(3.0, title = 'Large Liquidation Level')
h2 = input(2.0, title = 'Middle Liquidation Level')
h1 = input(1.2, title = 'Small Liquidation Level')

// Display options
showLine = input(true, title = 'Show lines')
showHist = input(true, title = 'Show histogram')
showLocalOnly = input(true, title = 'Only show local liquidation levels')

// Leverage visibility controls
show5x = input(true, 'Show 5x Leverage', group = 'Leverage Visibility')
show10x = input(true, 'Show 10x Leverage', group = 'Leverage Visibility')
show25x = input(true, 'Show 25x Leverage', group = 'Leverage Visibility')
show50x = input(true, 'Show 50x Leverage', group = 'Leverage Visibility')
show100x = input(true, 'Show 100x Leverage', group = 'Leverage Visibility')

// Color settings
i_5xColor = input.color(color.rgb(179, 181, 190, 25), '5x Leverage color', group = '5x Leverage')
i_10xColor = input.color(color.rgb(1, 124, 255, 30), '10x Leverage color', group = '10x Leverage')
i_25xColor = input.color(color.rgb(91, 176, 86, 30), '25x Leverage color', group = '25x Leverage')
i_50xColor = input.color(color.rgb(240, 161, 41, 30), '50x Leverage color', group = '50x Leverage')
i_100xColor = input.color(color.rgb(218, 55, 101, 30), '100x Leverage color', group = '100x Leverage')

// Histogram settings
barColor = input(color.rgb(120, 123, 134, 50), 'Bar Color')
numOfBars = input(120, 'Number of bars to lookback')
distLastCandle = input(5, 'Histogram distance from last candle')
numOfHistograms = input(120, 'Number of histograms (<=120)')

// Enhanced calculation settings
priceMethod = input.string("VWAP", "Price Calculation Method", options=["Simple Average", "VWAP", "Typical Price"], group="Enhanced Calculations")
includeFunding = input(true, "Include Funding Rate Estimate", group="Enhanced Calculations")
fundingRate = input(0.0001, "Estimated Funding Rate (8h)", group="Enhanced Calculations") * 3 // Convert to daily
maintenanceMarginRate = input(0.005, "Maintenance Margin Rate", group="Enhanced Calculations")
liquidationFeeRate = input(0.0005, "Liquidation Fee Rate", group="Enhanced Calculations")

// ===== DATA FETCHING AND PROCESSING =====
OI = request.security(userSymbol, timeframe.period, close)
OI_delta = OI - nz(OI[1])

// Enhanced moving averages
OI_delta_MA = ta.sma(OI_delta, maLength)
OI_delta_abs = math.abs(OI_delta)
OI_delta_abs_MA = ta.sma(OI_delta_abs, maLength)

// Liquidation trigger conditions
OI_delta_open_h3 = OI_delta_abs >= OI_delta_abs_MA * h3 and OI_delta > 0
OI_delta_open_h2 = OI_delta_abs >= OI_delta_abs_MA * h2 and OI_delta_abs < OI_delta_abs_MA * h3 and OI_delta > 0
OI_delta_open_h1 = OI_delta_abs >= OI_delta_abs_MA * h1 and OI_delta_abs < OI_delta_abs_MA * h2 and OI_delta > 0

// ===== ENHANCED PRICE CALCULATION =====
f_getPrice() =>
    switch priceMethod
        "Simple Average" => (open + close + high + low) / 4
        "VWAP" => ta.vwap(hlc3)
        "Typical Price" => hlc3
        => hlc3

kline_price = f_getPrice()

// ===== ENHANCED LIQUIDATION CALCULATION =====
f_calculateEnhancedLeverage(_pivotValue, _shortSell, _leverage) =>
    baseMargin = 1 / _leverage
    totalMarginRate = baseMargin + maintenanceMarginRate + liquidationFeeRate
    fundingAdjustment = includeFunding ? fundingRate / 365 : 0 // Daily funding impact

    if _shortSell
        _pivotValue * (1 - totalMarginRate - fundingAdjustment)
    else
        _pivotValue * (1 + totalMarginRate + fundingAdjustment)

// ===== OPTIMIZED ARRAY MANAGEMENT =====
var h3Array = array.new<line>()
var h2Array = array.new<line>()
var h1Array = array.new<line>()

// Histogram arrays declared early to avoid scope issues
var histogramLowList = array.new<float>(numOfHistograms, na)
var histogramHighList = array.new<float>(numOfHistograms, na)
var histogramData = array.new<float>()
var histogramTargetList = array.new<float>(numOfHistograms, 0.0)
var Bars = array.new<box>(numOfHistograms, na)

// Maximum array sizes to prevent memory issues
MAX_LINES = 200

f_cleanupArray(_array) =>
    while array.size(_array) > MAX_LINES
        line.delete(array.shift(_array))

// ===== OPTIMIZED DRAWING FUNCTIONS =====
f_drawLine(_x1, _x2, _yValue, _lineColor, _style, _width) =>
    line.new(x1 = _x1, y1 = _yValue, x2 = _x2, y2 = _yValue, color = _lineColor, style = _style, width = _width)

// Improved line extension and data collection
f_extendArray(_lineArray, _extendLines) =>
    if array.size(_lineArray) > 0
        for _i = array.size(_lineArray) - 1 to 0 by 1
            currentLine = array.get(_lineArray, _i)
            if not na(currentLine)
                x2 = line.get_x2(currentLine)
                yValue = line.get_y1(currentLine)
                // Extend line if it's active or if we're extending lines
                if _extendLines or (x2 >= bar_index - 1 and not(high > yValue and low < yValue))
                    line.set_x2(currentLine, bar_index + 1)

// New function to collect histogram data from all active lines
f_collectHistogramData(_lineArray, _weight = 1.0) =>
    if array.size(_lineArray) > 0
        for _i = 0 to array.size(_lineArray) - 1 by 1
            currentLine = array.get(_lineArray, _i)
            if not na(currentLine)
                x2 = line.get_x2(currentLine)
                yValue = line.get_y1(currentLine)
                // Collect data from lines that are still active (extended to current bar)
                if x2 >= bar_index - 1
                    // Add value with weight to histogram data
                    for w = 1 to math.max(1, math.floor(_weight)) by 1
                        histogramData.push(yValue)

f_append(_array, _yValue, _color, _style, _width) =>
    if array.size(_array) >= numOfLines
        line.delete(array.shift(_array))
    l = f_drawLine(bar_index, bar_index, _yValue, _color, _style, _width)
    array.push(_array, l)

// Enhanced leverage line drawing with density-based styling
drawEnhancedLeverageLine(_array, _color, _style, _leverage, _baseWidth, _visible) =>
    if _visible
        var float yValueLong = na
        var float yValueShort = na

        yValueLong := f_calculateEnhancedLeverage(kline_price, false, _leverage)
        yValueShort := f_calculateEnhancedLeverage(kline_price, true, _leverage)

        // Dynamic line width based on OI delta strength
        dynamicWidth = _baseWidth + math.floor(OI_delta_abs / OI_delta_abs_MA)

        f_append(_array, yValueLong, _color, _style, dynamicWidth)
        f_append(_array, yValueShort, _color, _style, dynamicWidth)

// ===== MAIN LIQUIDATION LEVEL DRAWING =====
if OI_delta_open_h3
    drawEnhancedLeverageLine(h3Array, i_5xColor, line.style_solid, 5, 3, show5x)
    drawEnhancedLeverageLine(h3Array, i_10xColor, line.style_solid, 10, 3, show10x)
    drawEnhancedLeverageLine(h3Array, i_25xColor, line.style_solid, 25, 3, show25x)
    drawEnhancedLeverageLine(h3Array, i_50xColor, line.style_solid, 50, 3, show50x)
    drawEnhancedLeverageLine(h3Array, i_100xColor, line.style_solid, 100, 3, show100x)

if OI_delta_open_h2
    drawEnhancedLeverageLine(h2Array, i_10xColor, line.style_solid, 10, 2, show10x)
    drawEnhancedLeverageLine(h2Array, i_25xColor, line.style_solid, 25, 2, show25x)
    drawEnhancedLeverageLine(h2Array, i_50xColor, line.style_solid, 50, 2, show50x)
    drawEnhancedLeverageLine(h2Array, i_100xColor, line.style_solid, 100, 2, show100x)

if OI_delta_open_h1
    drawEnhancedLeverageLine(h1Array, i_25xColor, line.style_dotted, 25, 1, show25x)
    drawEnhancedLeverageLine(h1Array, i_50xColor, line.style_dotted, 50, 1, show50x)
    drawEnhancedLeverageLine(h1Array, i_100xColor, line.style_dotted, 100, 1, show100x)

// Cleanup arrays to prevent memory issues
f_cleanupArray(h3Array)
f_cleanupArray(h2Array)
f_cleanupArray(h1Array)

// ===== IMPROVED HISTOGRAM PROCESSING =====
local_high = ta.highest(high, numOfBars)
local_low = ta.lowest(low, numOfBars)
rangeHigh = local_high * (1 + local_high / local_low / 10)
rangeLow = local_low * (1 - local_high / local_low / 10)
rangeHeight = rangeHigh - rangeLow
histogramHeight = rangeHeight / numOfHistograms

// Extend lines first
f_extendArray(h3Array, false)
f_extendArray(h2Array, false)
f_extendArray(h1Array, false)

// Helper function to find maximum index in array
f_findMaxIndex(_array, _start, _end) =>
    _maxValue = array.get(_array, _start)
    _maxIndex = _start
    for _i = _start + 1 to _end by 1
        if array.get(_array, _i) > _maxValue
            _maxValue := array.get(_array, _i)
            _maxIndex := _i
    _maxIndex

// ===== ENHANCED HISTOGRAM RENDERING =====
if barstate.islast and showHist
    // Clean previous boxes efficiently
    for i = 0 to numOfHistograms - 1 by 1
        existingBox = array.get(Bars, i)
        if not na(existingBox)
            box.delete(existingBox)
        array.set(Bars, i, na)

    // Reset and collect histogram data with weights
    array.clear(histogramData)
    for i = 0 to numOfHistograms - 1 by 1
        array.set(histogramTargetList, i, 0.0)

    // Collect data from line arrays with different weights based on importance
    f_collectHistogramData(h3Array, 3.0)  // High importance liquidations
    f_collectHistogramData(h2Array, 2.0)  // Medium importance liquidations
    f_collectHistogramData(h1Array, 1.0)  // Low importance liquidations

    // Define histogram boundaries
    for i = 0 to numOfHistograms - 1 by 1
        histogramLow = rangeLow + histogramHeight * i
        histogramHigh = rangeLow + histogramHeight * (i + 1)
        array.set(histogramLowList, i, histogramLow)
        array.set(histogramHighList, i, histogramHigh)

    // Count liquidation levels in each histogram bin
    for y in histogramData
        for j = 0 to numOfHistograms - 1 by 1
            histogramLow = array.get(histogramLowList, j)
            histogramHigh = array.get(histogramHighList, j)
            if y >= histogramLow and y <= histogramHigh
                currentCount = array.get(histogramTargetList, j)
                array.set(histogramTargetList, j, currentCount + 1)
                break  // Exit inner loop once we find the right bin

    // Find maximum and significant clusters
    maxHistogramIndex = f_findMaxIndex(histogramTargetList, 0, numOfHistograms - 1)
    maxHistogramValue = array.get(histogramTargetList, maxHistogramIndex)
    significantThreshold = math.max(1, maxHistogramValue * 0.3)  // 30% of max value

    // Render histogram bars with improved styling
    for i = 0 to numOfHistograms - 1 by 1
        histogramLow = array.get(histogramLowList, i)
        histogramHigh = array.get(histogramHighList, i)
        histogramTarget = array.get(histogramTargetList, i)

        if histogramTarget > 0
            // Calculate bar width based on relative importance
            relativeImportance = histogramTarget / maxHistogramValue
            histogramWidth = math.max(1, math.floor(relativeImportance * 8) + 1)

            // Determine visibility - show max cluster and significant levels
            isMaxCluster = i == maxHistogramIndex
            isSignificant = histogramTarget >= significantThreshold

            if isMaxCluster or isSignificant
                // Enhanced color coding based on cluster density and importance
                _color = color.new(barColor, 50)
                if isMaxCluster
                    _color := color.new(color.red, 20)
                else if histogramTarget >= maxHistogramValue * 0.7
                    _color := color.new(color.orange, 30)
                else if histogramTarget >= maxHistogramValue * 0.5
                    _color := color.new(color.yellow, 40)

                // Create histogram bar
                newBox = box.new(left = bar_index + distLastCandle, top = histogramHigh, right = bar_index + distLastCandle + histogramWidth, bottom = histogramLow, bgcolor = _color, border_color = _color, border_width = isMaxCluster ? 2 : 1)
                array.set(Bars, i, newBox)

                // Add text label for max cluster
                if isMaxCluster and histogramTarget >= 3
                    labelText = str.tostring(math.floor(histogramTarget))
                    label.new(x = bar_index + distLastCandle + histogramWidth + 1, y = (histogramHigh + histogramLow) / 2, text = labelText, color = color.new(color.white, 100), textcolor = color.red, size = size.small, style = label.style_label_left)

// ===== LINE VISIBILITY MANAGEMENT =====
if barstate.islast and not showLine
    // Clear all line arrays if lines are disabled
    for line_obj in h3Array
        line.delete(line_obj)
    for line_obj in h2Array
        line.delete(line_obj)
    for line_obj in h1Array
        line.delete(line_obj)
    array.clear(h3Array)
    array.clear(h2Array)
    array.clear(h1Array)

else if barstate.islast and showLocalOnly
    // Remove lines outside the visible range
    for i = array.size(h3Array) - 1 to 0 by 1
        currentLine = array.get(h3Array, i)
        if not na(currentLine)
            linePrice = line.get_y1(currentLine)
            if linePrice < rangeLow or linePrice > rangeHigh
                line.delete(currentLine)
                array.remove(h3Array, i)

    for i = array.size(h2Array) - 1 to 0 by 1
        currentLine = array.get(h2Array, i)
        if not na(currentLine)
            linePrice = line.get_y1(currentLine)
            if linePrice < rangeLow or linePrice > rangeHigh
                line.delete(currentLine)
                array.remove(h2Array, i)

    for i = array.size(h1Array) - 1 to 0 by 1
        currentLine = array.get(h1Array, i)
        if not na(currentLine)
            linePrice = line.get_y1(currentLine)
            if linePrice < rangeLow or linePrice > rangeHigh
                line.delete(currentLine)
                array.remove(h1Array, i)

// ===== ENHANCED ALERT SYSTEM =====
var float prevMaxHistogramIndex = na
currentMaxHistogramIndex = f_findMaxIndex(histogramTargetList, 0, numOfHistograms - 1)
var bool alertCondition = false

// Enhanced alert logic
if not na(currentMaxHistogramIndex) and currentMaxHistogramIndex != prevMaxHistogramIndex
    alertCondition := true
else
    alertCondition := false

prevMaxHistogramIndex := currentMaxHistogramIndex

// Alert with enhanced message
alertcondition(alertCondition, title = 'New Maximum Liquidation Cluster', message = 'New max liquidation cluster found at index: {{plot("currentMaxHistogramIndex")}} with enhanced calculations including funding rates and maintenance margins')

// Debug plot for the current max histogram index (hidden by default)
plot(currentMaxHistogramIndex, title="currentMaxHistogramIndex", display=display.none)
